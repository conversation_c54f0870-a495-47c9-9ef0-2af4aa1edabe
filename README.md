# Project Overview: Document Data Extraction

## 1. High-Level Goal

This project provides a robust, asynchronous pipeline for extracting structured data from documents. It leverages cloud services and various Large Language Models (LLMs) to process files uploaded to an S3 bucket, storing the extracted data in a searchable format.

## 2. Core Technologies

- **Backend Framework**: FastAPI
- **Cloud Services**: AWS S3 (for storage), AWS SQS (for message queuing)
- **Database / Search**: OpenSearch (for data indexing and vector search)
- **LLM Integration**: OpenAI, AWS Bedrock, Azure OpenAI (via LangChain)
- **Async Processing**: `asyncio`, `aioboto3` for non-blocking I/O
- **Containerization**: Docker, Docker Compose

## 3. Architecture Overview

The application follows an event-driven, asynchronous architecture:

1.  **Upload**: A user or process uploads a document (e.g., PDF, image) to a designated AWS S3 bucket.
2.  **Trigger**: The S3 upload event triggers a notification to an AWS SQS queue. There are separate queues for different processing tasks (e.g., `extraction`, `classification`).
3.  **Consume & Process**: Dedicated worker services (`extraction-worker`, `classification-worker`) poll their respective SQS queues for new messages.
4.  **LLM Call**: Upon receiving a message, the worker fetches the corresponding document from S3 and sends it to a configured LLM for data extraction or classification.
5.  **Store Results**: The worker processes the LLM's JSON output. The structured data is then:
    -   Saved as a new JSON file in a separate S3 output bucket.
    -   Potentially indexed in OpenSearch for complex queries and similarity search.

## 4. Project Structure

- **/app**: The core FastAPI application code.
  - **/api**: Defines the API endpoints, request/response models, and dependencies.
  - **/core**: Contains core application logic, including configuration (`settings`) and connections to external services like OpenSearch.
  - **/llm**: Manages interaction with the different LLM providers.
  - **/services**: Contains the business logic for the asynchronous workers that process documents (e.g., `document_extraction_processor.py`).
  - **/crud**: Handles data access operations (Create, Read, Update, Delete) for OpenSearch.
  - **/main.py**: The entry point for the FastAPI application.
- **/scripts**: Contains utility scripts, for example, to initialize the OpenSearch index.
- **`docker-compose.yml`**: Defines and configures all the services required to run the application stack (API, workers, OpenSearch).
- **`Dockerfile`**: Defines the container image for the Python application.

## 5. Getting Started

To run the project locally for review:

1.  **Prerequisites**: Docker and Docker Compose must be installed.
2.  **Environment**: Create a `.env` file from the `env.example` template and fill in the necessary credentials for AWS and at least one LLM provider.
3.  **Build & Run**: Execute the following command from the project root:
    ```bash
    docker-compose up -d --build
    ```
4.  **Access services**: Start the background service for extraction or classification inside app/services [eg: python document_extraction_processor.py ].

## 6. Architecture Diagram

You can render the following Mermaid code in a compatible editor (like GitHub's Markdown viewer or a free online tool) to visualize the project architecture.

```mermaid
graph TD
    %% User Interaction
    subgraph "User Interaction"
        User["User"]
    end

    %% AWS Cloud
    subgraph "AWS Cloud"
        S3Input["S3 Bucket (Input)"]
        S3Input --> S3Extraction["extraction-input/"]
        S3Input --> S3Classification["classification-input/"]

        SQSExtraction["SQS Queue (Extraction)"]
        SQSClassification["SQS Queue (Classification)"]

        S3OutputExtraction["S3 Bucket (Extraction Output)"]
        S3OutputClassification["S3 Bucket (Classification Output)"]
    end

    %% Application Services
    subgraph "Application Services (Docker)"
        FastAPI["FastAPI API"]
        ExtractionWorker["Extraction Worker"]
        ClassificationWorker["Classification Worker"]
    end

    %% External Services
    subgraph "External Services"
        LLM["LLM API (OpenAI / Bedrock / Azure)"]
    end

    %% Flow
    User -- "Uploads Document" --> FastAPI
    FastAPI -- "Stores File" --> S3Input

    %% Extraction Flow
    S3Extraction -- "Triggers Event" --> SQSExtraction
    SQSExtraction -- "Consumed by" --> ExtractionWorker
    ExtractionWorker -- "Fetches Document" --> S3Extraction
    ExtractionWorker -- "Processes with" --> LLM
    LLM -- "Returns JSON" --> ExtractionWorker
    ExtractionWorker -- "Stores JSON Output" --> S3OutputExtraction

    %% Classification Flow
    S3Classification -- "Triggers Event" --> SQSClassification
    SQSClassification -- "Consumed by" --> ClassificationWorker
    ClassificationWorker -- "Fetches Document" --> S3Classification
    ClassificationWorker -- "Processes with" --> LLM
    LLM -- "Returns Classification" --> ClassificationWorker
    ClassificationWorker -- "Stores Classified PDFs" --> S3OutputClassification

    %% Styling
    classDef aws fill:#FF9900,stroke:#333,stroke-width:2px,color:#fff;
    class S3Input,S3Extraction,S3Classification,SQSExtraction,SQSClassification,S3OutputExtraction,S3OutputClassification aws;

    classDef app fill:#264DE4,stroke:#333,stroke-width:2px,color:#fff;
    class FastAPI,ExtractionWorker,ClassificationWorker app;

    classDef external fill:#4CAF50,stroke:#333,stroke-width:2px,color:#fff;
    class LLM external;


```